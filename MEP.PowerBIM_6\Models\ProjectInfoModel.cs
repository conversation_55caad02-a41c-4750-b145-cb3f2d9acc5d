using CommunityToolkit.Mvvm.ComponentModel;

namespace MEP.PowerBIM_6.Models
{
    /// <summary>
    /// Model representing project information for WPF binding
    /// </summary>
    public partial class ProjectInfoModel : ObservableObject
    {
        [ObservableProperty]
        private string _jobName = string.Empty;

        [ObservableProperty]
        private string _engineer = string.Empty;

        [ObservableProperty]
        private double _systemVoltageDropMax = 5.0;

        [ObservableProperty]
        private int _ambientTemperature = 30;
    }

    /// <summary>
    /// Stub model for circuit data
    /// </summary>
    public partial class CircuitModel : ObservableObject
    {
        [ObservableProperty]
        private string _circuitNumber = string.Empty;

        [ObservableProperty]
        private double _load;

        [ObservableProperty]
        private bool _isManualOverride;
    }

    /// <summary>
    /// Stub model for settings
    /// </summary>
    public partial class SettingsModel : ObservableObject
    {
        [ObservableProperty]
        private bool _autoCalculate = true;

        [ObservableProperty]
        private string _defaultExportPath = string.Empty;
    }

    /// <summary>
    /// Stub model for export settings
    /// </summary>
    public partial class ExportSettingsModel : ObservableObject
    {
        [ObservableProperty]
        private string _outputPath = string.Empty;

        [ObservableProperty]
        private bool _includeImages;
    }
}
