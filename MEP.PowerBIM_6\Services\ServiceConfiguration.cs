using System;
using Autodesk.Revit.UI;
using BecaActivityLogger.CoreLogic.Data;
using MEP.PowerBIM_6.Services.Interfaces;
using MEP.PowerBIM_6.ViewModels;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace MEP.PowerBIM_6.Services
{
    /// <summary>
    /// Service configuration for dependency injection container
    /// Configures all services and ViewModels for PowerBIM 6 WPF application
    /// </summary>
    public static class ServiceConfiguration
    {
        /// <summary>
        /// Configure all services for the application
        /// </summary>
        /// <param name="services">Service collection to configure</param>
        /// <param name="uiDocument">Current UI document</param>
        /// <param name="logger">Activity logger</param>
        /// <returns>Configured service collection</returns>
        public static IServiceCollection ConfigureServices(this IServiceCollection services, UIDocument uiDocument, BecaActivityLoggerData logger)
        {
            // Register core dependencies
            services.AddSingleton(uiDocument);
            services.AddSingleton(uiDocument.Document);
            services.AddSingleton(uiDocument.Application);
            services.AddSingleton(logger);

            // Configure logging
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                builder.AddDebug();
                builder.SetMinimumLevel(LogLevel.Information);
            });

            // Register business services
            RegisterBusinessServices(services);

            // Register ViewModels
            RegisterViewModels(services);

            return services;
        }

        /// <summary>
        /// Register all business services
        /// </summary>
        /// <param name="services">Service collection</param>
        private static void RegisterBusinessServices(IServiceCollection services)
        {
            // Core services
            services.AddTransient<IRevitService, RevitService>();
            services.AddTransient<IDataService, DataService>();
            services.AddTransient<IExportService, ExportService>();
            services.AddTransient<IImportService, ImportService>();
            services.AddTransient<ICalculationService, CalculationService>();
            services.AddTransient<IPathEditingService, PathEditingService>();
            services.AddTransient<IDialogService, DialogService>();

            // Utility services
            services.AddSingleton<ICacheService, CacheService>();
            services.AddTransient<IValidationService, ValidationService>();
            services.AddTransient<ISettingsService, SettingsService>();
        }

        /// <summary>
        /// Register all ViewModels
        /// </summary>
        /// <param name="services">Service collection</param>
        private static void RegisterViewModels(IServiceCollection services)
        {
            // Main ViewModels
            services.AddTransient<MainViewModel>();
            services.AddTransient<CircuitEditViewModel>();
            services.AddTransient<DbEditViewModel>();
            services.AddTransient<AdvancedSettingsViewModel>();
            services.AddTransient<DbSettingsViewModel>();
            services.AddTransient<ExportViewModel>();
            services.AddTransient<ImportSettingsViewModel>();

            // Item ViewModels
            services.AddTransient<DatabaseItemViewModel>();
            services.AddTransient<CircuitItemViewModel>();
            services.AddTransient<ProjectInfoViewModel>();
        }

        /// <summary>
        /// Build and configure the service provider
        /// </summary>
        /// <param name="uiDocument">Current UI document</param>
        /// <param name="logger">Activity logger</param>
        /// <returns>Configured service provider</returns>
        public static IServiceProvider BuildServiceProvider(UIDocument uiDocument, BecaActivityLoggerData logger)
        {
            var services = new ServiceCollection();
            services.ConfigureServices(uiDocument, logger);
            return services.BuildServiceProvider();
        }
    }

    /// <summary>
    /// Additional service interfaces that need to be implemented
    /// </summary>
    
    /// <summary>
    /// Service for import operations
    /// </summary>
    public interface IImportService
    {
        Task<bool> ImportFromCsvAsync(string filePath, ImportSettings settings);
        Task<List<ImportMatchResult>> ProcessImportMatchingAsync(string filePath, List<string> existingItems);
        ValidationResult ValidateImportFile(string filePath);
    }

    /// <summary>
    /// Service for calculation operations
    /// </summary>
    public interface ICalculationService
    {
        Task<bool> RecalculateDatabase(object database);
        Task<bool> RunCircuitCalculations(object circuit);
        Task<bool> UpdateCircuitProperties(object circuit);
    }

    /// <summary>
    /// Service for path editing operations
    /// </summary>
    public interface IPathEditingService
    {
        Task<bool> OpenPathCustomizingView();
        Task<bool> ActivatePathEditView();
        Task<bool> SetManualLength(object circuit, double length);
    }

    /// <summary>
    /// Service for dialog operations
    /// </summary>
    public interface IDialogService
    {
        Task<bool> ShowMessageAsync(string title, string message);
        Task<bool> ShowConfirmationAsync(string title, string message);
        Task<string> ShowSaveFileDialogAsync(string filter, string defaultFileName = null);
        Task<string> ShowOpenFileDialogAsync(string filter);
        void ShowWindow<TViewModel>(TViewModel viewModel) where TViewModel : BaseViewModel;
    }

    /// <summary>
    /// Service for caching operations
    /// </summary>
    public interface ICacheService
    {
        T Get<T>(string key) where T : class;
        void Set<T>(string key, T value, TimeSpan? expiration = null) where T : class;
        void Remove(string key);
        void Clear();
    }

    /// <summary>
    /// Service for validation operations
    /// </summary>
    public interface IValidationService
    {
        ValidationResult ValidateProjectInfo(object projectInfo);
        ValidationResult ValidateDatabase(object database);
        ValidationResult ValidateCircuit(object circuit);
        ValidationResult ValidateSettings(object settings);
    }

    /// <summary>
    /// Service for settings management
    /// </summary>
    public interface ISettingsService
    {
        Task<T> LoadSettingsAsync<T>() where T : class, new();
        Task<bool> SaveSettingsAsync<T>(T settings) where T : class;
        T GetDefaultSettings<T>() where T : class, new();
        void ResetToDefaults<T>() where T : class, new();
    }

    /// <summary>
    /// Import settings for CSV import operations
    /// </summary>
    public class ImportSettings
    {
        public bool HasHeaders { get; set; } = true;
        public string Delimiter { get; set; } = ",";
        public Dictionary<string, int> ColumnMapping { get; set; } = new Dictionary<string, int>();
        public bool OverwriteExisting { get; set; } = false;
    }

    /// <summary>
    /// Result of import matching operation
    /// </summary>
    public class ImportMatchResult
    {
        public string ImportItem { get; set; }
        public string MatchedItem { get; set; }
        public double MatchConfidence { get; set; }
        public bool IsExactMatch { get; set; }
        public List<string> PossibleMatches { get; set; } = new List<string>();
    }
}
