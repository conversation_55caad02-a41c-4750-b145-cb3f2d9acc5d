using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using MEP.PowerBIM_5.CoreLogic;
using MEP.PowerBIM_6.Models;

namespace MEP.PowerBIM_6.Services.Interfaces
{
    /// <summary>
    /// Service interface for all Revit API operations
    /// Provides thread-safe access to Revit API functionality through ExternalEvent architecture
    /// </summary>
    public interface IRevitService
    {
        #region Project Information Operations

        /// <summary>
        /// Save project information to Revit parameters
        /// </summary>
        /// <param name="projectInfo">Project information to save</param>
        /// <returns>True if successful</returns>
        Task<bool> SaveProjectInfoAsync(ProjectInfoModel projectInfo);

        /// <summary>
        /// Load project information from Revit parameters
        /// </summary>
        /// <returns>Project information model</returns>
        Task<ProjectInfoModel> LoadProjectInfoAsync();

        /// <summary>
        /// Commit project settings to Revit document
        /// </summary>
        /// <param name="projectInfo">Project information to commit</param>
        /// <returns>True if successful</returns>
        Task<bool> CommitProjectInfoAsync(PowerBIM_ProjectInfo projectInfo);

        #endregion

        #region Distribution Board Operations

        /// <summary>
        /// Load all distribution boards from the current Revit document
        /// </summary>
        /// <returns>List of distribution board models</returns>
        Task<List<DistributionBoardModel>> LoadDistributionBoardsAsync();

        /// <summary>
        /// Save distribution board information to Revit
        /// </summary>
        /// <param name="distributionBoard">Distribution board to save</param>
        /// <returns>True if successful</returns>
        Task<bool> SaveDistributionBoardAsync(DistributionBoardModel distributionBoard);

        /// <summary>
        /// Refresh distribution board summary information
        /// </summary>
        /// <param name="distributionBoards">Distribution boards to refresh</param>
        /// <returns>Updated distribution board list</returns>
        Task<List<DistributionBoardModel>> RefreshDistributionBoardSummaryAsync(List<DistributionBoardModel> distributionBoards);

        #endregion

        #region Circuit Operations

        /// <summary>
        /// Update circuits for specified distribution boards
        /// </summary>
        /// <param name="distributionBoards">Distribution boards containing circuits to update</param>
        /// <returns>True if successful</returns>
        Task<bool> UpdateCircuitsAsync(List<DistributionBoardModel> distributionBoards);

        /// <summary>
        /// Save circuit data to Revit
        /// </summary>
        /// <param name="circuits">Circuits to save</param>
        /// <returns>True if successful</returns>
        Task<bool> SaveCircuitDataAsync(List<CircuitModel> circuits);

        /// <summary>
        /// Recalculate circuits for a specific distribution board
        /// </summary>
        /// <param name="distributionBoard">Distribution board to recalculate</param>
        /// <returns>True if successful</returns>
        Task<bool> RecalculateCircuitsAsync(DistributionBoardModel distributionBoard);

        /// <summary>
        /// Perform bulk edit operations on lighting circuits
        /// </summary>
        /// <param name="distributionBoard">Distribution board containing circuits</param>
        /// <param name="settings">Bulk edit settings</param>
        /// <returns>Number of circuits updated</returns>
        Task<int> BulkEditLightingAsync(DistributionBoardModel distributionBoard, BulkEditSettings settings);

        /// <summary>
        /// Perform bulk edit operations on power circuits
        /// </summary>
        /// <param name="distributionBoard">Distribution board containing circuits</param>
        /// <param name="settings">Bulk edit settings</param>
        /// <returns>Number of circuits updated</returns>
        Task<int> BulkEditPowerAsync(DistributionBoardModel distributionBoard, BulkEditSettings settings);

        #endregion

        #region Path Editing Operations

        /// <summary>
        /// Open path customizing view for circuit editing
        /// </summary>
        /// <param name="circuit">Circuit to edit path for</param>
        /// <returns>True if successful</returns>
        Task<bool> OpenPathCustomizingViewAsync(CircuitModel circuit);

        /// <summary>
        /// Activate path edit view in Revit
        /// </summary>
        /// <returns>True if successful</returns>
        Task<bool> ActivatePathEditViewAsync();

        /// <summary>
        /// Set manual length for a circuit
        /// </summary>
        /// <param name="circuit">Circuit to update</param>
        /// <param name="length">Manual length value</param>
        /// <returns>True if successful</returns>
        Task<bool> SetCircuitLengthManualAsync(CircuitModel circuit, double length);

        #endregion

        #region Element Selection and Validation

        /// <summary>
        /// Check if required elements exist in the document
        /// </summary>
        /// <param name="document">Document to check</param>
        /// <returns>True if all required elements exist</returns>
        Task<bool> RequiredElementsExistAsync(Document document);

        /// <summary>
        /// Get selected electrical equipment from Revit
        /// </summary>
        /// <returns>List of selected electrical equipment</returns>
        Task<List<Element>> GetSelectedElectricalEquipmentAsync();

        /// <summary>
        /// Validate shared parameter file exists
        /// </summary>
        /// <returns>True if shared parameter file exists</returns>
        Task<bool> ValidateSharedParameterFileAsync();

        #endregion

        #region Transaction Management

        /// <summary>
        /// Execute an operation within a Revit transaction
        /// </summary>
        /// <param name="transactionName">Name of the transaction</param>
        /// <param name="operation">Operation to execute</param>
        /// <returns>True if successful</returns>
        Task<bool> ExecuteInTransactionAsync(string transactionName, Func<bool> operation);

        /// <summary>
        /// Execute an operation within a Revit transaction (REVIT-SAFE: Synchronous)
        /// </summary>
        /// <param name="transactionName">Name of the transaction</param>
        /// <param name="operation">Operation to execute</param>
        /// <returns>True if successful</returns>
        bool ExecuteInTransaction(string transactionName, Func<bool> operation);

        #endregion

        #region Document Properties

        /// <summary>
        /// Get the current Revit document
        /// </summary>
        Document Document { get; }

        /// <summary>
        /// Get the current UI document
        /// </summary>
        UIDocument UIDocument { get; }

        /// <summary>
        /// Get the current UI application
        /// </summary>
        UIApplication UIApplication { get; }

        #endregion
    }

    /// <summary>
    /// Settings for bulk edit operations
    /// </summary>
    public class BulkEditSettings
    {
        public string CircuitType { get; set; }
        public double? LoadValue { get; set; }
        public string LoadUnit { get; set; }
        public bool OverwriteExisting { get; set; }
        public Dictionary<string, object> CustomSettings { get; set; } = new Dictionary<string, object>();
    }
}
