using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.Revit.UI;
using BecaActivityLogger.CoreLogic.Data;
using MEP.PowerBIM_5.CoreLogic;
using MEP.PowerBIM_6.Models;
using MEP.PowerBIM_6.Services.Interfaces;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace MEP.PowerBIM_6.Handlers
{
    /// <summary>
    /// Enhanced request handler for PowerBIM 6 WPF application
    /// Implements IExternalEventHandler to safely execute Revit API operations
    /// Uses dependency injection for clean service access
    /// </summary>
    public class RequestHandler_PB6 : IExternalEventHandler
    {
        #region Fields

        private readonly IServiceProvider _serviceProvider;
        private readonly BecaActivityLoggerData _logger;
        private readonly Request_PB6_Configure _request;
        private readonly ILogger<RequestHandler_PB6> _systemLogger;

        // Specialized handlers for complex operations (to be implemented)
        // private readonly EditCircuitPathClicker_WPF _circuitPathClicker;
        // private readonly EditDBPathClicker_WPF _dbPathClicker;

        #endregion

        #region Properties

        /// <summary>
        /// Get the request configuration object
        /// </summary>
        public Request_PB6_Configure Request => _request;

        #endregion

        #region Constructor

        /// <summary>
        /// Initialize the request handler with dependency injection
        /// </summary>
        /// <param name="serviceProvider">Service provider for accessing services</param>
        /// <param name="logger">Activity logger for tracking operations</param>
        public RequestHandler_PB6(IServiceProvider serviceProvider, BecaActivityLoggerData logger)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _request = new Request_PB6_Configure();
            _systemLogger = serviceProvider.GetService<ILogger<RequestHandler_PB6>>();

            // TODO: Initialize specialized handlers
            // var uiDocument = serviceProvider.GetRequiredService<UIDocument>();
            // _circuitPathClicker = new EditCircuitPathClicker_WPF(uiDocument.Application);
            // _dbPathClicker = new EditDBPathClicker_WPF(uiDocument.Application);

            _systemLogger?.LogInformation("RequestHandler_PB6 initialized");
        }

        #endregion

        #region IExternalEventHandler Implementation

        /// <summary>
        /// Get the name of this external event handler
        /// </summary>
        /// <returns>Handler name</returns>
        public string GetName()
        {
            return "PowerBIM 6 Request Handler";
        }

        /// <summary>
        /// Execute the pending request in the Revit API context
        /// This method is called by Revit when the ExternalEvent is raised
        /// </summary>
        /// <param name="uiapp">UI application from Revit</param>
        public void Execute(UIApplication uiapp)
        {
            try
            {
                var requestId = _request.Take();
                _systemLogger?.LogDebug($"Executing request: {requestId}");

                switch (requestId)
                {
                    case RequestId_PB6.None:
                        return; // No request to process

                    // Project Operations
                    case RequestId_PB6.SaveProject:
                        HandleSaveProject();
                        break;

                    case RequestId_PB6.SaveSettings:
                        HandleSaveSettings();
                        break;

                    case RequestId_PB6.CommitProjectInfo:
                        HandleCommitProjectInfo();
                        break;

                    case RequestId_PB6.LoadProjectInfo:
                        HandleLoadProjectInfo();
                        break;

                    // Database Operations
                    case RequestId_PB6.LoadDatabases:
                        HandleLoadDatabases();
                        break;

                    case RequestId_PB6.SaveDatabase:
                        HandleSaveDatabase();
                        break;

                    case RequestId_PB6.RefreshDatabaseSummary:
                        HandleRefreshDatabaseSummary();
                        break;

                    case RequestId_PB6.UpdaterRequired_All:
                        HandleUpdaterRequiredAll();
                        break;

                    // Circuit Operations
                    case RequestId_PB6.UpdateCircuits:
                        HandleUpdateCircuits();
                        break;

                    case RequestId_PB6.RecalculateCircuits:
                        HandleRecalculateCircuits();
                        break;

                    case RequestId_PB6.BulkEditLighting:
                        HandleBulkEditLighting();
                        break;

                    case RequestId_PB6.BulkEditPower:
                        HandleBulkEditPower();
                        break;

                    // Path Editing Operations
                    case RequestId_PB6.OpenPathCustomizing:
                        HandleOpenPathCustomizing();
                        break;

                    case RequestId_PB6.ActivatePathEditView:
                        HandleActivatePathEditView();
                        break;

                    // Export Operations
                    case RequestId_PB6.ExportData:
                        HandleExportData();
                        break;

                    case RequestId_PB6.ExportCircuitImages:
                        HandleExportCircuitImages();
                        break;

                    // UI Operations
                    case RequestId_PB6.WakeFormUp:
                    case RequestId_PB6.RefreshData:
                        HandleWakeFormUp();
                        break;

                    default:
                        _systemLogger?.LogWarning($"Unhandled request: {requestId}");
                        _logger?.LogWarning($"Unhandled request: {requestId}");
                        break;
                }
            }
            catch (Exception ex)
            {
                _systemLogger?.LogError(ex, "Request execution failed");
                _logger?.LogError($"Request execution failed: {ex.Message}");
            }
            finally
            {
                // Always wake up the UI
                WakeUpMainWindow();
            }
        }

        #endregion

        #region Request Handlers

        /// <summary>
        /// Handle save project request
        /// </summary>
        private void HandleSaveProject()
        {
            try
            {
                var revitService = _serviceProvider.GetRequiredService<IRevitService>();
                var projectInfo = ModelessMainWindowHandler.ProjectInfo;

                if (projectInfo != null)
                {
                    // REVIT-SAFE: Execute synchronously in Revit context
                    projectInfo.CommitToRevit();
                    _logger?.LogInfo("Project information saved successfully");
                }
            }
            catch (Exception ex)
            {
                _systemLogger?.LogError(ex, "Failed to save project");
                _logger?.LogError($"Failed to save project: {ex.Message}");
            }
        }

        /// <summary>
        /// Handle save settings request
        /// </summary>
        private void HandleSaveSettings()
        {
            try
            {
                var revitService = _serviceProvider.GetRequiredService<IRevitService>();
                var projectInfo = ModelessMainWindowHandler.ProjectInfo;

                if (projectInfo != null)
                {
                    // Save settings and commit to Revit
                    projectInfo.CommitProjectInfo();
                    _logger?.LogInfo("Settings saved successfully");
                }
            }
            catch (Exception ex)
            {
                _systemLogger?.LogError(ex, "Failed to save settings");
                _logger?.LogError($"Failed to save settings: {ex.Message}");
            }
        }

        /// <summary>
        /// Handle commit project info request
        /// </summary>
        private void HandleCommitProjectInfo()
        {
            try
            {
                var projectInfo = ModelessMainWindowHandler.ProjectInfo;
                if (projectInfo != null)
                {
                    projectInfo.CommitProjectInfo();
                    _logger?.LogInfo("Project info committed successfully");
                }
            }
            catch (Exception ex)
            {
                _systemLogger?.LogError(ex, "Failed to commit project info");
                _logger?.LogError($"Failed to commit project info: {ex.Message}");
            }
        }

        /// <summary>
        /// Handle load project info request
        /// </summary>
        private void HandleLoadProjectInfo()
        {
            try
            {
                var revitService = _serviceProvider.GetRequiredService<IRevitService>();
                var projectInfoModel = revitService.LoadProjectInfo();

                if (projectInfoModel != null)
                {
                    // Update the static reference for cross-form communication
                    ModelessMainWindowHandler.ProjectInfo = projectInfoModel.OriginalData;
                    _logger?.LogInfo("Project info loaded successfully");
                }
                else
                {
                    _logger?.LogWarning("Project info could not be loaded");
                }
            }
            catch (Exception ex)
            {
                _systemLogger?.LogError(ex, "Failed to load project info");
                _logger?.LogError($"Failed to load project info: {ex.Message}");
            }
        }

        /// <summary>
        /// Handle load databases request
        /// </summary>
        private void HandleLoadDatabases()
        {
            try
            {
                var revitService = _serviceProvider.GetRequiredService<IRevitService>();
                var distributionBoardModels = revitService.LoadDistributionBoards();

                if (distributionBoardModels?.Any() == true)
                {
                    // Convert models back to original data for cross-form communication
                    var originalDatabases = distributionBoardModels
                        .Select(model => model.OriginalData)
                        .Where(data => data != null)
                        .ToList();

                    ModelessMainWindowHandler.AllDatabases = originalDatabases;
                    _logger?.LogInfo($"Loaded {originalDatabases.Count} distribution boards successfully");
                }
                else
                {
                    _logger?.LogWarning("No distribution boards found in the model");
                    ModelessMainWindowHandler.AllDatabases = new List<PowerBIM_DBData>();
                }
            }
            catch (Exception ex)
            {
                _systemLogger?.LogError(ex, "Failed to load databases");
                _logger?.LogError($"Failed to load databases: {ex.Message}");
            }
        }

        /// <summary>
        /// Handle save database request
        /// </summary>
        private void HandleSaveDatabase()
        {
            try
            {
                var revitService = _serviceProvider.GetRequiredService<IRevitService>();
                var databases = ModelessMainWindowHandler.AllDatabases;

                if (databases?.Any() == true)
                {
                    int savedCount = 0;
                    foreach (var database in databases)
                    {
                        var distributionBoardModel = new DistributionBoardModel(database);
                        if (revitService.SaveDistributionBoard(distributionBoardModel))
                        {
                            savedCount++;
                        }
                    }

                    _logger?.LogInfo($"Saved {savedCount} of {databases.Count} distribution boards successfully");
                }
                else
                {
                    _logger?.LogWarning("No databases available to save");
                }
            }
            catch (Exception ex)
            {
                _systemLogger?.LogError(ex, "Failed to save database");
                _logger?.LogError($"Failed to save database: {ex.Message}");
            }
        }

        /// <summary>
        /// Handle refresh database summary request
        /// </summary>
        private void HandleRefreshDatabaseSummary()
        {
            try
            {
                var revitService = _serviceProvider.GetRequiredService<IRevitService>();
                var databases = ModelessMainWindowHandler.AllDatabases;

                if (databases?.Any() == true)
                {
                    // Convert to models for processing
                    var distributionBoardModels = databases
                        .Select(db => new DistributionBoardModel(db))
                        .ToList();

                    // Refresh summary information
                    var refreshedModels = revitService.RefreshDistributionBoardSummary(distributionBoardModels);

                    // Update the static reference with refreshed data
                    ModelessMainWindowHandler.AllDatabases = refreshedModels
                        .Select(model => model.OriginalData)
                        .Where(data => data != null)
                        .ToList();

                    _logger?.LogInfo($"Database summary refreshed for {refreshedModels.Count} distribution boards");
                }
                else
                {
                    _logger?.LogWarning("No databases available to refresh");
                }
            }
            catch (Exception ex)
            {
                _systemLogger?.LogError(ex, "Failed to refresh database summary");
                _logger?.LogError($"Failed to refresh database summary: {ex.Message}");
            }
        }

        /// <summary>
        /// Handle updater required all request
        /// </summary>
        private void HandleUpdaterRequiredAll()
        {
            try
            {
                var revitService = _serviceProvider.GetRequiredService<IRevitService>();
                var databases = ModelessMainWindowHandler.AllDatabases;

                if (databases?.Any() == true)
                {
                    // Convert to models for processing
                    var distributionBoardModels = databases
                        .Select(db => new DistributionBoardModel(db))
                        .ToList();

                    // Update all circuits that require updates
                    bool success = revitService.UpdateCircuits(distributionBoardModels);

                    if (success)
                    {
                        _logger?.LogInfo($"Updater required all processed successfully for {distributionBoardModels.Count} distribution boards");
                    }
                    else
                    {
                        _logger?.LogWarning("Updater required all completed with warnings");
                    }
                }
                else
                {
                    _logger?.LogWarning("No databases available for updater required all");
                }
            }
            catch (Exception ex)
            {
                _systemLogger?.LogError(ex, "Failed to process updater required all");
                _logger?.LogError($"Failed to process updater required all: {ex.Message}");
            }
        }

        /// <summary>
        /// Handle update circuits request
        /// </summary>
        private void HandleUpdateCircuits()
        {
            try
            {
                var calculationService = _serviceProvider.GetRequiredService<ICalculationService>();
                var databases = ModelessMainWindowHandler.AllDatabases;

                if (databases != null)
                {
                    foreach (var db in databases.Where(d => !d.IsManuallyLocked))
                    {
                        // REVIT-SAFE: Execute synchronously in Revit context
                        calculationService.RecalculateDistributionBoard(db);
                    }
                    _logger?.LogInfo("Circuits updated successfully");
                }
            }
            catch (Exception ex)
            {
                _systemLogger?.LogError(ex, "Failed to update circuits");
                _logger?.LogError($"Failed to update circuits: {ex.Message}");
            }
        }

        /// <summary>
        /// Handle recalculate circuits request
        /// </summary>
        private void HandleRecalculateCircuits()
        {
            try
            {
                var revitService = _serviceProvider.GetRequiredService<IRevitService>();
                var databases = ModelessMainWindowHandler.AllDatabases;

                if (databases?.Any() == true)
                {
                    int recalculatedCount = 0;
                    foreach (var database in databases.Where(db => !db.IsManuallyLocked))
                    {
                        var distributionBoardModel = new DistributionBoardModel(database);
                        if (revitService.RecalculateCircuits(distributionBoardModel))
                        {
                            recalculatedCount++;
                        }
                    }

                    _logger?.LogInfo($"Recalculated circuits for {recalculatedCount} distribution boards");
                }
                else
                {
                    _logger?.LogWarning("No databases available for circuit recalculation");
                }
            }
            catch (Exception ex)
            {
                _systemLogger?.LogError(ex, "Failed to recalculate circuits");
                _logger?.LogError($"Failed to recalculate circuits: {ex.Message}");
            }
        }

        /// <summary>
        /// Handle bulk edit lighting request
        /// </summary>
        private void HandleBulkEditLighting()
        {
            try
            {
                var revitService = _serviceProvider.GetRequiredService<IRevitService>();
                var databases = ModelessMainWindowHandler.AllDatabases;

                if (databases?.Any() == true)
                {
                    int totalUpdated = 0;
                    var bulkEditSettings = new BulkEditSettings(); // Default settings

                    foreach (var database in databases.Where(db => !db.IsManuallyLocked))
                    {
                        var distributionBoardModel = new DistributionBoardModel(database);
                        int updated = revitService.BulkEditLighting(distributionBoardModel, bulkEditSettings);
                        totalUpdated += updated;
                    }

                    _logger?.LogInfo($"Bulk edit lighting completed - updated {totalUpdated} circuits");
                }
                else
                {
                    _logger?.LogWarning("No databases available for bulk edit lighting");
                }
            }
            catch (Exception ex)
            {
                _systemLogger?.LogError(ex, "Failed to bulk edit lighting");
                _logger?.LogError($"Failed to bulk edit lighting: {ex.Message}");
            }
        }

        /// <summary>
        /// Handle bulk edit power request
        /// </summary>
        private void HandleBulkEditPower()
        {
            try
            {
                var revitService = _serviceProvider.GetRequiredService<IRevitService>();
                var databases = ModelessMainWindowHandler.AllDatabases;

                if (databases?.Any() == true)
                {
                    int totalUpdated = 0;
                    var bulkEditSettings = new BulkEditSettings(); // Default settings

                    foreach (var database in databases.Where(db => !db.IsManuallyLocked))
                    {
                        var distributionBoardModel = new DistributionBoardModel(database);
                        int updated = revitService.BulkEditPower(distributionBoardModel, bulkEditSettings);
                        totalUpdated += updated;
                    }

                    _logger?.LogInfo($"Bulk edit power completed - updated {totalUpdated} circuits");
                }
                else
                {
                    _logger?.LogWarning("No databases available for bulk edit power");
                }
            }
            catch (Exception ex)
            {
                _systemLogger?.LogError(ex, "Failed to bulk edit power");
                _logger?.LogError($"Failed to bulk edit power: {ex.Message}");
            }
        }

        /// <summary>
        /// Handle open path customizing request
        /// </summary>
        private void HandleOpenPathCustomizing()
        {
            try
            {
                var pathEditingService = _serviceProvider.GetRequiredService<IPathEditingService>();
                // REVIT-SAFE: Execute synchronously in Revit context
                pathEditingService.OpenPathCustomizingView();
                _logger?.LogInfo("Path customizing view opened");
            }
            catch (Exception ex)
            {
                _systemLogger?.LogError(ex, "Failed to open path customizing view");
                _logger?.LogError($"Failed to open path customizing view: {ex.Message}");
            }
        }

        /// <summary>
        /// Handle activate path edit view request
        /// </summary>
        private void HandleActivatePathEditView()
        {
            try
            {
                var pathEditingService = _serviceProvider.GetRequiredService<IPathEditingService>();
                // REVIT-SAFE: Execute synchronously in Revit context
                pathEditingService.ActivatePathEditView();
                _logger?.LogInfo("Path edit view activated");
            }
            catch (Exception ex)
            {
                _systemLogger?.LogError(ex, "Failed to activate path edit view");
                _logger?.LogError($"Failed to activate path edit view: {ex.Message}");
            }
        }

        /// <summary>
        /// Handle export data request
        /// </summary>
        private void HandleExportData()
        {
            try
            {
                var exportService = _serviceProvider.GetRequiredService<IExportService>();
                var databases = ModelessMainWindowHandler.AllDatabases;

                if (databases?.Any() == true)
                {
                    // Create default export settings
                    var exportSettings = new ExportSettingsModel();
                    exportSettings.ResetToDefaults();

                    // Export to Excel by default
                    bool success = exportService.ExportToExcel(exportSettings);

                    if (success)
                    {
                        _logger?.LogInfo($"Data exported successfully for {databases.Count} distribution boards");
                    }
                    else
                    {
                        _logger?.LogWarning("Data export completed with warnings");
                    }
                }
                else
                {
                    _logger?.LogWarning("No data available to export");
                }
            }
            catch (Exception ex)
            {
                _systemLogger?.LogError(ex, "Failed to export data");
                _logger?.LogError($"Failed to export data: {ex.Message}");
            }
        }

        /// <summary>
        /// Handle export circuit images request
        /// </summary>
        private void HandleExportCircuitImages()
        {
            try
            {
                var exportService = _serviceProvider.GetRequiredService<IExportService>();
                var databases = ModelessMainWindowHandler.AllDatabases;

                if (databases?.Any() == true)
                {
                    // Collect all circuits from all databases
                    var allCircuits = new List<CircuitModel>();
                    foreach (var database in databases)
                    {
                        if (database.Circuits?.Any() == true)
                        {
                            var circuitModels = database.Circuits
                                .Select(circuit => new CircuitModel(circuit))
                                .ToList();
                            allCircuits.AddRange(circuitModels);
                        }
                    }

                    if (allCircuits.Any())
                    {
                        // Export circuit path images to default location
                        string outputPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
                        bool success = exportService.ExportCircuitPathImages(allCircuits, outputPath);

                        if (success)
                        {
                            _logger?.LogInfo($"Circuit images exported successfully for {allCircuits.Count} circuits");
                        }
                        else
                        {
                            _logger?.LogWarning("Circuit image export completed with warnings");
                        }
                    }
                    else
                    {
                        _logger?.LogWarning("No circuits found to export images for");
                    }
                }
                else
                {
                    _logger?.LogWarning("No databases available for circuit image export");
                }
            }
            catch (Exception ex)
            {
                _systemLogger?.LogError(ex, "Failed to export circuit images");
                _logger?.LogError($"Failed to export circuit images: {ex.Message}");
            }
        }

        /// <summary>
        /// Handle wake form up request
        /// </summary>
        private void HandleWakeFormUp()
        {
            // This is handled in the finally block
            _systemLogger?.LogDebug("Wake form up request processed");
        }

        /// <summary>
        /// Wake up the main window after request processing
        /// </summary>
        private void WakeUpMainWindow()
        {
            try
            {
                ModelessMainWindowHandler.WakeUpMainWindow();
            }
            catch (Exception ex)
            {
                _systemLogger?.LogError(ex, "Failed to wake up main window");
            }
        }

        #endregion
    }
}
